import {Flex, Space, Typography, Divider} from 'antd';
import styled from '@emotion/styled';
import {useCallback} from 'react';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import {useMCPServerId} from '@/components/MCP/hooks';
import {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
import MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {IconEye} from '@/icons/mcp';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import DescriptionItem from '@/design/MCP/MCPDescriptionItem';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';

const DepartmentText = styled.div`
    margin: 4px 0 14px;
    color: #8F8F8F;
`;

const MCPInfo = () => {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);

    const refresh = useCallback(
        () => {
            loadMCPServer({mcpServerId});
        },
        [mcpServerId]
    );

    return (
        <Flex vertical gap={16}>
            <Flex gap={14}>
                <MCPServerAvatar size={88} icon={mcpServer?.icon} />
                <Flex vertical justify="space-between" style={{flex: 1}}>
                    <Flex justify="space-between">
                        <Typography.Title level={3}>{mcpServer?.name}</Typography.Title>
                        <Space split={<Divider type="vertical" style={{borderColor: '#D9D9D9'}} />}>
                            <Flex align="center" gap={4}>
                                <IconEye style={{marginRight: 4}} />
                                {mcpServer?.serverMetrics?.viewCount ?? 0}
                            </Flex>
                            <MCPCollectButton
                                refresh={refresh}
                                favorite={mcpServer?.favorite}
                                serverId={mcpServerId}
                            />
                            <MCPSubscribeButton
                                id={mcpServerId}
                                workspaceId={mcpServer?.workspaceId}
                                showText
                            />
                        </Space>
                    </Flex>
                    <DepartmentText>{mcpServer?.departmentName || '暂无部门信息'}</DepartmentText>
                    <Flex align="center" gap={40}>
                        <DescriptionItem label="类型">
                            <MCPServerTypeTag type={mcpServer?.serverProtocolType} />
                        </DescriptionItem>
                        <DescriptionItem label="协议">
                            <MCPServerProtocolTypeTag type={mcpServer?.serverProtocolType} />
                        </DescriptionItem>
                        <DescriptionItem label="场景">
                            <TagGroup
                                labels={(mcpServer?.labels || []).map(
                                    label => ({id: label.id, label: label.labelValue})
                                )}
                                color="light-purple"
                                maxNum={3}
                                prefix={null}
                            />
                        </DescriptionItem>
                        <DescriptionItem label="联系人">
                            <UserAvatarList users={mcpServer?.contacts ?? []} max={2} />
                        </DescriptionItem>
                    </Flex>
                </Flex>
            </Flex>
            <Typography.Paragraph type="secondary">
                {mcpServer?.description || '暂无描述'}
            </Typography.Paragraph>
        </Flex>
    );
};

export default MCPInfo;
