import {Tag} from '@panda-design/components';
import {MCPServerProtocolType} from '@/types/mcp/mcp';
import {IconLocal, IconRemote} from '@/icons/mcp';

export const SERVER_TYPE_DICT: Record<MCPServerProtocolType, string> = {
    SSE: 'remote',
    STDIO: 'local',
    Streamable_HTTP: 'remote',
};

export const getServerTypeText = (type: MCPServerProtocolType) => {
    return SERVER_TYPE_DICT[type] || 'remote';
};

export const getServerTypeIcon = (type: MCPServerProtocolType) => {
    switch (type) {
        case 'STDIO':
            return <IconLocal />;
        case 'SSE':
        case 'Streamable_HTTP':
            return <IconRemote />;
        default:
            return <IconRemote />;
    }
};

interface Props {
    type: MCPServerProtocolType;
}
export default function MCPServerTypeTag({type}: Props) {
    return (
        <Tag
            type="flat"
            style={{
                color: '#3779EA',
                backgroundColor: '#DFE9FC',
                margin: 0,
            }}
            icon={getServerTypeIcon(type)}
        >
            {SERVER_TYPE_DICT[type]}
        </Tag>
    );
}
