import styled from '@emotion/styled';
import {CSSProperties} from 'react';
import {getServerTypeIcon, getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import {MCPServerProtocolType} from '@/types/mcp/mcp';

const Wrapper = styled.span`
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #DFE9FC;
    border-top-right-radius: 6px;
    border-bottom-left-radius: 6px;
    color: #3678E9;
    font-size: 12px;
    line-height: 24px;
    height: 24px;
    padding: 0 8px 0 4px;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: -15px;
        top: 0;
        width: 0;
        height: 0;
        border-left: 18px solid transparent;
        border-top: 24px solid #DFE9FC;
        border-bottom-right-radius: 4px;
    };
`;

interface Props {
    type: MCPServerProtocolType;
    style?: CSSProperties;
}
export default function MCPServerTypeTag({type, style}: Props) {

    return (
        <Wrapper style={style}>
            {getServerTypeIcon(type)}
            <span>{getServerTypeText(type)}</span>
        </Wrapper>
    );
}
